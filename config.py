from pydantic_settings import BaseSettings
from pydantic import ConfigD<PERSON>
from typing import ClassVar, Literal, Optional
import os
import sys
from functools import lru_cache

def get_environment() -> str:
    """
    Get the environment setting using multiple fallback methods:
    1. Command line argument (--env)
    2. ENVIRONMENT environment variable
    3. Default to 'development'
    """
    # Check command line arguments
    for i, arg in enumerate(sys.argv[1:]):
        if arg == "--env" and i + 2 < len(sys.argv):
            return sys.argv[i + 2]
        elif arg.startswith("--env="):
            return arg.split("=")[1]

    # Check environment variable or use default
    return os.getenv("ENVIRONMENT") or "development"

class Settings(BaseSettings):
    # Environment
    environment: Literal["development", "staging", "production"] = get_environment()

    # User Agent for external requests
    user_agent: str = "Assivy-Backend/1.0.0"

    # Application URL
    app_url: str = "http://localhost:8000"  # Backend URL

    # Database
    mongo_url: str = "mongodb+srv://namnhihg:<EMAIL>/?retryWrites=true&w=majority&appName=dev-data"
    testing_mode: bool = False  # Set to True to use MockedDBRepository instead of MongoDBRepository

    # JWT Configuration
    secret_key: str = "YOUR-SECRET-KEY-HERE"  # DEPRECATED: Use JWT_SECRET_KEY environment variable
    jwt_secret_key: Optional[str] = None  # Secure JWT secret key (min 32 chars)
    jwt_algorithm: str = "HS256"
    jwt_issuer: str = "assivy-api"
    jwt_audience: str = "assivy-client"
    jwt_access_token_expire_minutes: int = 120  # 2 hours
    jwt_refresh_token_expire_days: int = 30

    # OAuth / External API credentials
    google_client_id: str = "YOUR-GOOGLE"
    google_client_secret: str = "YOUR-GOOGLE-CLIENT-SECRET"

    # AI Model Vendor API Keys
    openai_api_key: Optional[str] = "YOUR-OPEN-AI-KEY" 
    google_ai_key: str = "AIzaSyB_LHToe8sscEv_Df0Z7yfxHn9l3U1lmFQ"
    anthropic_api_key: str = "YOUR-ANTHROPIC-API-KEY"

    # Zilliz Cloud settings
    zilliz_uri: str = "https://in03-47c25729510a7c4.serverless.gcp-us-west1.cloud.zilliz.com"
    zilliz_token: str = "32b8a04f5487100ab84ff6a4ed9f4c0db80c69158b177127a8866da9351464af41329e81d7e28de8ce8a7fefc6ed76da93e87c2f"

    # Rate Limiter Settings - Added type annotations
    DEFAULT_RATE_LIMIT: int = int(os.getenv("DEFAULT_RATE_LIMIT", "100"))  # requests per window
    DEFAULT_WINDOW: int = int(os.getenv("DEFAULT_WINDOW", "60"))  # window size in seconds

    # AstraDB settings
    astra_api_endpoint: str  = "https://300ba0ce-e72f-482d-9e82-66c0f82ff634-us-east-2.apps.astra.datastax.com"
    astra_token: str = "AstraCS:CwUEmmNHOJpfhUHZNSSUDTOq:d1d4bea23b01f9a8d887978fec52c9513de59b25d695da93a3ed3203b35a4261"

    # Weaviate settings (both cloud and local support)
    weaviate_cluster_url: str = "https://your-cluster.weaviate.network"
    weaviate_api_key: Optional[str] = "your-weaviate-api-key"
    weaviate_url: str = "http://localhost:8080"  # For local development
    weaviate_use_cloud: bool = True  # Set to False for local Weaviate
    
    # Text processing configuration
    chunk_size: int = 1000  # Characters per chunk
    chunk_overlap: int = 200  # Overlap between chunks
    
    # System Initialization Settings
    platform_tenant_name: str = "Assivy Platform"
    customer_tenant_name: str = "Assivy Customer Test"
    system_admin_email: str = "<EMAIL>"
    system_admin_password: str = "SystemAdmin123!"
    customer_admin_email: str = "<EMAIL>"
    customer_admin_password: str = "Administrator123!"

    # File processing thresholds
    small_file_threshold_mb: int = 5  # Files under this size process immediately
    large_file_threshold_mb: int = 50  # Files over this size use background processing
    max_file_size_mb: int = 100  # Maximum allowed file size
    
    # Web crawling configuration
    web_crawl_delay: float = 1.0  # Delay between requests (seconds)
    web_crawl_timeout: int = 30  # Request timeout (seconds)
    web_crawl_max_pages: int = 100  # Maximum pages per crawl
    web_crawl_respect_robots: bool = True  # Respect robots.txt
    
    # File Storage Configuration
    storage_provider: Literal["minio", "s3", "local"] = "minio"  # Storage backend
    
    # MinIO Settings (for local/self-hosted object storage)
    minio_endpoint: str = "localhost:9000"
    minio_access_key: str = "minioadmin"  # Default MinIO credentials
    minio_secret_key: str = "minioadmin"
    minio_secure: bool = False  # Use HTTPS (False for local dev)
    minio_bucket_name: str = "assivy-files"
    
    # AWS S3 Settings (for cloud storage)
    aws_access_key_id: Optional[str] = None
    aws_secret_access_key: Optional[str] = None
    aws_region: str = "us-east-1"
    aws_s3_bucket_name: str = "assivy-files"
    
    # Local Storage Settings (for development)
    local_storage_path: str = "./uploads"
    upload_dir: str = "./uploads"  # For file uploads (profile images, organization logos, etc.)
    
    # Storage configuration
    storage_max_file_size_mb: int = 100  # Maximum file size
    storage_allowed_extensions: list = [
        # Documents
        ".pdf", ".docx", ".doc", ".txt", ".md", ".rtf", ".odt",
        # Code
        ".py", ".js", ".ts", ".html", ".css", ".java", ".cpp", ".c", ".go", ".rs",
        # Data
        ".json", ".csv", ".xml", ".yaml", ".yml", ".jsonl",
        # Spreadsheets
        ".xlsx", ".xls", ".ods",
        # Presentations
        ".pptx", ".ppt", ".odp",
        # Images (for OCR)
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff",
        # Archives
        ".zip", ".tar", ".gz", ".rar"
    ]

    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore"  # Ignore extra environment variables (like VITE_* for frontend)
    )

settings = Settings()