from typing import Optional, Dict, Any, List, TypeVar, Generic, get_type_hints, get_origin, get_args
import logging
from config import settings
from uuid import UUID, uuid4
from datetime import datetime, timezone
from fastapi import HTTPException, status
from data_access.base import BaseRepository, ModelType
from models.registry import get_collection_name, model_registry
from models.base import BaseModel, BaseModelWithTenant
import weaviate
from weaviate.util import generate_uuid5
from weaviate.classes.config import Configure, Property, DataType
from weaviate.classes.query import Filter
from pydantic import BaseModel as PydanticBaseModel
from enum import Enum
import json
import typing
from typing import get_origin, get_args, Union

logger = logging.getLogger(__name__)

WEAVIATE_RESERVED_FIELDS = {'_additional', 'id', '_id', '_vector', 'vector'}

def _serialize_nested_models(data: dict, model_class: type = None) -> dict:
    """Convert nested Pydantic models to JSON strings for Weaviate storage"""
    serialized = {}
    
    # Get model fields info if available
    model_fields = {}
    if model_class and hasattr(model_class, 'model_fields'):
        model_fields = model_class.model_fields
    
    for key, value in data.items():
        if isinstance(value, PydanticBaseModel):
            # Serialize Pydantic models to JSON strings
            serialized[key] = value.model_dump_json()
        elif isinstance(value, list) and value and isinstance(value[0], PydanticBaseModel):
            # Serialize list of Pydantic models to JSON strings
            serialized[key] = [item.model_dump_json() for item in value]
        elif isinstance(value, UUID):
            # Directly handle UUID values
            serialized[key] = str(value)
        elif isinstance(value, list) and value and isinstance(value[0], UUID):
            # Handle lists of UUIDs
            serialized[key] = [str(item) for item in value]
        elif isinstance(value, dict):
            # Check if this dict represents a nested Pydantic model
            if key in model_fields:
                field_info = model_fields[key]
                field_type = field_info.annotation
                
                # If the field type is a Pydantic model, serialize the dict as JSON
                if (isinstance(field_type, type) and 
                    issubclass(field_type, PydanticBaseModel)):
                    # Use custom JSON encoder to handle datetime objects
                    try:
                        serialized[key] = json.dumps(value, default=_json_serializer)
                    except Exception as e:
                        logger.warning(f"Failed to serialize {key}: {e}")
                        # Fall back to the original value
                        serialized[key] = value
                else:
                    # Recursively handle other nested dictionaries
                    serialized[key] = _serialize_nested_models(value)
            else:
                # Recursively handle nested dictionaries
                serialized[key] = _serialize_nested_models(value)
        elif isinstance(value, list) and value and isinstance(value[0], dict):
            # Check if this is a list of nested models
            if key in model_fields:
                field_info = model_fields[key]
                field_type = field_info.annotation
                
                # If it's a list of Pydantic models, serialize each dict as JSON
                if (get_origin(field_type) == list and
                    get_args(field_type) and
                    isinstance(get_args(field_type)[0], type) and
                    issubclass(get_args(field_type)[0], PydanticBaseModel)):
                    try:
                        serialized[key] = [json.dumps(item, default=_json_serializer) for item in value]
                    except Exception as e:
                        logger.warning(f"Failed to serialize {key}: {e}")
                        serialized[key] = value
                else:
                    serialized[key] = value
            else:
                serialized[key] = value
        else:
            serialized[key] = value
    return serialized

def _json_serializer(obj):
    """Custom JSON serializer for datetime objects"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, UUID):
        return str(obj)
    raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")


def _deserialize_nested_models(data: dict, model_class: type) -> dict:
    """Convert JSON strings back to Pydantic models when retrieving from Weaviate"""
    if not hasattr(model_class, 'model_fields'):
        return data
        
    deserialized = {}
    model_fields = model_class.model_fields
    
    for key, value in data.items():
        if key in model_fields:
            field_info = model_fields[key]
            field_type = field_info.annotation
            
            # Handle nested Pydantic models stored as JSON strings
            if (isinstance(value, str) and 
                isinstance(field_type, type) and 
                issubclass(field_type, PydanticBaseModel)):
                try:
                    model_data = json.loads(value)
                    deserialized[key] = field_type(**model_data)
                except (json.JSONDecodeError, TypeError):
                    deserialized[key] = value
            # Handle lists of nested models
            elif (isinstance(value, list) and value and 
                  isinstance(value[0], str) and
                  get_origin(field_type) == list and
                  get_args(field_type) and
                  isinstance(get_args(field_type)[0], type) and
                  issubclass(get_args(field_type)[0], PydanticBaseModel)):
                try:
                    inner_type = get_args(field_type)[0]
                    deserialized[key] = [inner_type(**json.loads(item)) for item in value]
                except (json.JSONDecodeError, TypeError):
                    deserialized[key] = value
            else:
                deserialized[key] = value
        else:
            deserialized[key] = value
    
    return deserialized

class WeaviateDB:
    """Weaviate connection manager"""
    client: weaviate.WeaviateAsyncClient = None

async def connect_to_weaviate():
    """Connect to Weaviate"""
    print("Connecting to Weaviate...")
    try:
        # Create Weaviate client
        client = weaviate.use_async_with_local()
        
        # Connect the client explicitly
        await client.connect()
        
        # Store client instance
        WeaviateDB.client = client
        
        print("Successfully connected to Weaviate!")
        
        # Set up schemas
        await setup_schemas()
    except Exception as e:
        logger.error(f"Error connecting to Weaviate: {str(e)}")
        raise

def _get_weaviate_data_type(field_type: type) -> DataType:
    """Convert Python type to Weaviate DataType, handling optional types."""
    
    # Check if the type is Optional (Union[T, None])
    origin = get_origin(field_type)
    if origin is Union:
        args = get_args(field_type)
        # Optional[T] is Union[T, None] or Union[T, type(None)]
        if len(args) == 2 and (type(None) in args or None in args):
            # Extract the non-None type
            field_type = next(arg for arg in args if arg is not type(None) and arg is not None)
    
    # Handle the actual type
    if field_type == str:
        return DataType.TEXT
    elif field_type == int:
        return DataType.INT
    elif field_type == float:
        return DataType.NUMBER
    elif field_type == bool:
        return DataType.BOOL
    elif field_type == datetime:
        return DataType.DATE
    elif field_type == list:
        return DataType.TEXT_ARRAY
    elif hasattr(field_type, '__origin__') and field_type.__origin__ == list:
        list_type = get_args(field_type)[0]
        if list_type == dict or (isinstance(list_type, type) and issubclass(list_type, PydanticBaseModel)):
            return DataType.OBJECT_ARRAY
        elif list_type == str:
            return DataType.TEXT_ARRAY
        elif list_type == int:
            return DataType.INT_ARRAY  
        elif list_type == float:
            return DataType.NUMBER_ARRAY
        else:
            return DataType.TEXT_ARRAY
    elif hasattr(field_type, '__origin__') and field_type.__origin__ == set:
        # Handle Set[T] types - treat as arrays since they get serialized to lists
        set_type = get_args(field_type)[0] if get_args(field_type) else str
        if set_type == str:
            return DataType.TEXT_ARRAY
        elif set_type == int:
            return DataType.INT_ARRAY
        elif set_type == float:
            return DataType.NUMBER_ARRAY
        else:
            # For complex types like enums, serialize as text array
            return DataType.TEXT_ARRAY
    elif field_type == dict:
        return DataType.OBJECT
    elif hasattr(field_type, '__origin__') and field_type.__origin__ == dict:
        return DataType.OBJECT
    elif isinstance(field_type, type) and issubclass(field_type, PydanticBaseModel):
        return DataType.OBJECT
    else:
        # Default fallback
        return DataType.TEXT

def _get_model_properties(model_class: type[PydanticBaseModel]) -> List[Property]:
    """Convert Pydantic model fields to Weaviate properties"""
    properties = []

    # Get the model's schema
    schema = model_class.model_json_schema()
    properties_schema = schema.get('properties', {})

    # Get field info from model
    for field_name, field_info in model_class.model_fields.items():
        # Skip private attributes and reserved fields
        if (field_name.startswith('_') or
            field_name in WEAVIATE_RESERVED_FIELDS):
            continue

        # Get field type from schema
        field_schema = properties_schema.get(field_name, {})
        field_type = field_info.annotation

        # Skip if field type is None (common for id fields)
        if field_type is None:
            continue

        # Create Weaviate property with validation
        data_type = _get_weaviate_data_type(field_type)
        property_kwargs = dict(
            name=field_name,
            data_type=data_type,
            description=field_info.description or field_schema.get('description', f"{field_name} field")
        )

        # If the field is a dict or Pydantic model, add nested_properties
        if data_type in (DataType.OBJECT, DataType.OBJECT_ARRAY):
            # If it's a Pydantic model, recurse
            if isinstance(field_type, type) and issubclass(field_type, PydanticBaseModel):
                property_kwargs['nested_properties'] = _get_model_properties(field_type)
            # If it's a dict, try to get the value type
            elif hasattr(field_type, '__origin__') and field_type.__origin__ == dict:
                value_type = get_args(field_type)[1] if len(get_args(field_type)) > 1 else str
                if isinstance(value_type, type) and issubclass(value_type, PydanticBaseModel):
                    property_kwargs['nested_properties'] = _get_model_properties(value_type)
                else:
                    # For plain dict[str, str], you could define a generic property
                    property_kwargs['nested_properties'] = [
                        Property(name="key", data_type=DataType.TEXT),
                        Property(name="value", data_type=_get_weaviate_data_type(value_type))
                    ]
            # If it's a list of Pydantic models or dicts
            elif hasattr(field_type, '__origin__') and field_type.__origin__ == list:
                list_type = get_args(field_type)[0]
                if isinstance(list_type, type) and issubclass(list_type, PydanticBaseModel):
                    property_kwargs['nested_properties'] = _get_model_properties(list_type)
                elif hasattr(list_type, '__origin__') and list_type.__origin__ == dict:
                    value_type = get_args(list_type)[1] if len(get_args(list_type)) > 1 else str
                    if isinstance(value_type, type) and issubclass(value_type, PydanticBaseModel):
                        property_kwargs['nested_properties'] = _get_model_properties(value_type)
                    else:
                        property_kwargs['nested_properties'] = [
                            Property(name="key", data_type=DataType.TEXT),
                            Property(name="value", data_type=_get_weaviate_data_type(value_type))
                        ]
                else:
                    # Fallback for list of dicts or unknown types
                    property_kwargs['nested_properties'] = [
                        Property(name="key", data_type=DataType.TEXT),
                        Property(name="value", data_type=DataType.TEXT)
                    ]
            else:
                # Fallback: at least one nested property is required
                property_kwargs['nested_properties'] = [
                    Property(name="key", data_type=DataType.TEXT),
                    Property(name="value", data_type=DataType.TEXT)
                ]

        try:
            property = Property(**property_kwargs)
            properties.append(property)
        except Exception as e:
            logger.warning(f"Skipping field {field_name} due to error: {str(e)}")
            continue

    return properties

async def setup_schemas():
    """Set up Weaviate schemas for all models"""
    try:
        # Ensure client is connected
        if not WeaviateDB.client.is_connected():
            await WeaviateDB.client.connect()
            
        # Get existing collections
        existing_collections = await WeaviateDB.client.collections.list_all()
            
        # Extract existing collection names
        existing_class_names = set()
        if isinstance(existing_collections, dict):
            existing_class_names = set(existing_collections.keys())
        else:
            existing_class_names = {collection.get('class', collection.get('name', '')) 
                                  for collection in existing_collections.get('classes', [])} if hasattr(existing_collections, 'get') else set()

        # Create schemas for each collection
        for model_type, collection_name in model_registry.items():
            try:
                if collection_name not in existing_class_names:
                    # Get properties from model schema
                    properties = _get_model_properties(model_type)
                    
                    # Check if model inherits from BaseModelWithTenant
                    is_tenant_model = issubclass(model_type, BaseModelWithTenant)
                    
                    # Create schema with multi-tenancy only for tenant-based models
                    config_args = {
                        "name": collection_name,
                        "properties": properties
                    }
                    
                    if is_tenant_model:
                        config_args["multi_tenancy_config"] = Configure.multi_tenancy(
                            enabled=True,
                            auto_tenant_creation=True
                        )
                    
                    await WeaviateDB.client.collections.create(**config_args)
                    print(f"Created collection: {collection_name} {'with' if is_tenant_model else 'without'} multi-tenancy")
                else:
                    print(f"Collection {collection_name} already exists")
                    
            except Exception as e:
                print(f"Error creating collection {collection_name}: {str(e)}")
                continue
                
    except Exception as e:
        print(f"Error in setup_schemas: {str(e)}")
        raise

async def close_weaviate_connection():
    """Close Weaviate connection"""
    print("Closing Weaviate connection...")
    if WeaviateDB.client:
        try:
            await WeaviateDB.client.close()
        except Exception as e:
            logger.error(f"Error closing Weaviate connection: {e}")
        finally:
            WeaviateDB.client = None
    print("Weaviate connection closed")

def get_client():
    """Get Weaviate client instance for dependency injection (synchronous version)"""
    if WeaviateDB.client is None:
        raise RuntimeError("Weaviate client not initialized. Call connect_to_weaviate() first.")
    return WeaviateDB.client

async def get_client_async():
    """Get Weaviate client instance for dependency injection (async version)"""
    if WeaviateDB.client is None:
        # Try to initialize the client if it's None
        await connect_to_weaviate()
    return WeaviateDB.client

async def ensure_client_connected():
    """Ensure the Weaviate client is connected"""
    if WeaviateDB.client is None:
        # Try to initialize the client if it's None
        await connect_to_weaviate()

    if not WeaviateDB.client.is_connected():
        await WeaviateDB.client.connect()

class WeaviateRepository(BaseRepository[ModelType]):
    """Weaviate implementation of the BaseRepository interface"""
    
    @classmethod
    async def from_model_type(cls, model_type: type, client, collection_name: str = None) -> 'WeaviateRepository[ModelType]':
        """Factory method to create a new repository instance with Weaviate client"""
        name = collection_name or get_collection_name(model_type)
        return cls(client, name, model_type)
    
    def __init__(self, client: weaviate.WeaviateAsyncClient, collection_name: str, model_type: type):
        self.client = client
        self.collection_name = collection_name
        self.model_type = model_type
        self.collection = client.collections.get(collection_name)

    @property
    def is_initialized(self) -> bool:
        return self.client is not None and self.collection is not None

    def _check_initialized(self):
        if not self.is_initialized:
            raise RuntimeError("WeaviateRepository not properly initialized. Use WeaviateRepository.from_model_type() factory method.")

    async def _ensure_connected(self):
        """Ensure client is connected before operations"""
        if not self.client.is_connected():
            await self.client.connect()

    def _should_apply_tenant_filter(self) -> bool:    
        # Check if the model inherits from BaseModelWithTenant
        from models.base import BaseModelWithTenant
        model_class = getattr(self, 'model', self.model_type)
        return issubclass(model_class, BaseModelWithTenant)

    def _get_tenant_query_filter(self) -> Dict[str, Any]:
        if not self._should_apply_tenant_filter():
            return {}
        return {"tenant_id": getattr(self, 'required_context').tenant_id}

    async def create(self, data: ModelType, tenant_id_for_creation: Optional[str] = None) -> ModelType:
        self._check_initialized()
        await self._ensure_connected()
        
        # Use simplified conversion
        doc_data = model_to_weaviate_properties(data)
        doc_data = _serialize_nested_models(doc_data, self.model_type)

        # Add audit fields (assuming these methods exist in base class)
        if hasattr(self, '_add_audit_fields'):
            self._add_audit_fields(doc_data)
        
        # Handle tenant ID
        tenant_id = None
        context = getattr(self, 'context', None)
        if tenant_id_for_creation and context:
            try:
                system_permissions = ["super_admin"]
                has_system_access = await context.has_any_permission(system_permissions)
                if has_system_access:
                    tenant_id = tenant_id_for_creation
            except Exception:
                pass
        elif self._should_apply_tenant_filter():
            tenant_id = getattr(self, 'required_context').tenant_id
        
        # Add metadata
        if hasattr(self, '_add_context_metadata'):
            self._add_context_metadata(doc_data)
        
        # Debug: log the data being sent
        logger.debug(f"Inserting data into Weaviate: {doc_data}")
        
        # Get collection with tenant context
        collection = self.collection
        if tenant_id:
            collection = collection.with_tenant(str(tenant_id))
        
        # Insert document into Weaviate
        try:
            insert_params = {"properties": doc_data}
            # If custom id exists, use it as uuid, or generate deterministic uuid5 if not a valid UUID
            custom_id = getattr(data, 'id', None)
            if custom_id:
                from weaviate.util import generate_uuid5
                import uuid
                try:
                    # Try to use as UUID
                    uuid.UUID(str(custom_id))
                    insert_params["uuid"] = str(custom_id)
                except Exception:
                    # Not a valid UUID, generate deterministic UUID5
                    insert_params["uuid"] = generate_uuid5(doc_data)
            weaviate_uuid = await collection.data.insert(**insert_params)
        except TypeError as e:
            logger.error(f"JSON serialization error in data insertion: {e}")
            logger.error(f"Data types: {[(k, type(v)) for k, v in doc_data.items()]}")
            raise
        
        return await self.get_by_id(weaviate_uuid)

    async def get_by_id(self, doc_id: UUID) -> Optional[ModelType]:
        self._check_initialized()
        await self._ensure_connected()
                
        try:
            # Get collection with tenant context if needed
            collection = self.collection
            if self._should_apply_tenant_filter():
                tenant_id = getattr(self, 'required_context').tenant_id
                collection = collection.with_tenant(str(tenant_id))
        
            result = await collection.query.fetch_object_by_id(doc_id)
            
            if not result:
                return None
            
            # Map Weaviate object to model using simplified approach
            if hasattr(result, 'properties'):
                # Handle datetime fields
                properties = result.properties
                                
                # Use simplified conversion
                weaviate_obj = {
                    "id": str(doc_id),
                    "properties": properties
                }
                return safe_weaviate_obj_to_model(weaviate_obj, self.model_type, 
                                                collection_name=self.collection_name, 
                                                obj_uuid=str(doc_id))
            else:
                # Handle raw dictionary response
                deserialized = _deserialize_nested_models(result, self.model_type)
                weaviate_obj = {
                    "id": str(doc_id),
                    "properties": deserialized
                }
                return safe_weaviate_obj_to_model(weaviate_obj, self.model_type, 
                                                collection_name=self.collection_name, 
                                                obj_uuid=str(doc_id))
                
        except Exception as e:
            logger.error(f"Error getting document by ID: {str(e)}")
            return None

    async def get_all(self, skip: int = 0, limit: int = 100, 
                    query_conditions: Optional[Dict[str, Any]] = None) -> List[ModelType]:
        self._check_initialized()
        await self._ensure_connected()

        # Validate parameters
        skip = max(0, int(skip)) if skip is not None else 0
        limit = max(1, min(int(limit), 1000)) if limit is not None else 100

        collection = self.collection
        if self._should_apply_tenant_filter():
            tenant_id = getattr(self, 'required_context').tenant_id
            collection = collection.with_tenant(str(tenant_id))

        where_filter = None
        if query_conditions:
            conditions = [
                Filter.by_property(key).equal(value)
                for key, value in query_conditions.items()
            ]
            if len(conditions) == 1:
                where_filter = conditions[0]
            else:
                where_filter = Filter.all_of(conditions)

        # Fetch using query
        # Use the correct parameter names for Weaviate v4
        try:
            logger.debug(f"Fetching objects with limit={limit}, offset={skip}, filter={where_filter is not None}")
            
            if where_filter is not None:
                result = await collection.query.fetch_objects(
                    limit=limit,
                    offset=skip,
                    filters=where_filter
                )
            else:
                result = await collection.query.fetch_objects(
                    limit=limit,
                    offset=skip
                )
        except Exception as e:
            logger.error(f"Error in fetch_objects: {str(e)} (type: {type(e).__name__})")
            logger.error(f"Parameters: limit={limit} (type: {type(limit)}), offset={skip} (type: {type(skip)})")
            
            # Try the simplest possible query
            try:
                logger.info("Trying simple fetch without parameters...")
                result = await collection.query.fetch_objects()
            except Exception as fallback_error:
                logger.error(f"Even simple fetch failed: {str(fallback_error)}")
                # Return empty result
                class EmptyResult:
                    objects = []
                result = EmptyResult()

        # Handle response format
        objects = []
        conversion_errors = 0
        
        if hasattr(result, 'objects'):
            for obj in result.objects:
                # Use simplified conversion
                weaviate_obj = {
                    "id": str(obj.uuid),
                    "properties": obj.properties
                }
                                
                # Create model instance using safe conversion approach
                model_obj = safe_weaviate_obj_to_model(
                    weaviate_obj, 
                    self.model_type, 
                    collection_name=self.collection_name,
                    obj_uuid=str(obj.uuid)
                )
                
                if model_obj is not None:
                    objects.append(model_obj)
                else:
                    conversion_errors += 1
        
        # Log summary if there were conversion errors
        if conversion_errors > 0:
            logger.warning(f"Failed to convert {conversion_errors} objects in collection {self.collection_name}")

        return objects

    async def get_list_with_count(self, skip: int = 0, limit: int = 100,
                                 query_conditions: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Get paginated entities with total count for pagination metadata.

        Args:
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return
            query_conditions: Optional additional query filters

        Returns:
            Dictionary containing paginated data and metadata
        """
        self._check_initialized()
        await self._ensure_connected()

        # Validate parameters
        skip = max(0, int(skip)) if skip is not None else 0
        limit = max(1, min(int(limit), 1000)) if limit is not None else 100

        collection = self.collection
        if self._should_apply_tenant_filter():
            tenant_id = getattr(self, 'required_context').tenant_id
            collection = collection.with_tenant(str(tenant_id))

        # Build where filter for both data and count queries
        where_filter = None
        if query_conditions:
            conditions = [
                Filter.by_property(key).equal(value)
                for key, value in query_conditions.items()
            ]
            if len(conditions) == 1:
                where_filter = conditions[0]
            else:
                where_filter = Filter.all_of(conditions)

        try:
            # Get paginated data using existing get_all logic
            data = await self.get_all(skip=skip, limit=limit, query_conditions=query_conditions)

            # Get total count using aggregate
            try:
                if where_filter is not None:
                    # For filtered queries, we need to use aggregate with the same filter
                    aggregate_result = await collection.aggregate.over_all(
                        filters=where_filter
                    )
                else:
                    # For unfiltered queries, get total count
                    aggregate_result = await collection.aggregate.over_all()

                total_count = aggregate_result.total_count or 0
            except Exception as count_error:
                logger.warning(f"Failed to get total count for collection {self.collection_name}: {count_error}")
                # Fallback: estimate based on current data
                total_count = len(data) if skip == 0 else skip + len(data)

            # Calculate pagination metadata
            has_more = (skip + len(data)) < total_count

            return {
                "data": data,
                "total_count": total_count,
                "skip": skip,
                "limit": limit,
                "has_more": has_more
            }

        except Exception as e:
            logger.error(f"Error in get_list_with_count for collection {self.collection_name}: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Error getting paginated data with count: {str(e)}"
            )

    async def update(self, doc_id: UUID, data: ModelType) -> Optional[ModelType]:
        self._check_initialized()
        await self._ensure_connected()
        
        # First check if document exists and is accessible
        existing_doc = await self.get_by_id(doc_id)
        if not existing_doc:
            return None

        # Prepare update data
        # Use simplified conversion for updates
        update_data = model_to_weaviate_properties(data)
        
        # Add audit fields
        if hasattr(self, '_add_audit_fields'):
            self._add_audit_fields(update_data, is_update=True)
        
        # Protect certain fields
        for protected_field in ["created_at", "created_by", "id", "_id"]:
            update_data.pop(protected_field, None)
            
        # Add metadata
        if hasattr(self, '_add_context_metadata'):
            self._add_context_metadata(update_data)

        # Get collection with tenant context if needed
        collection = self.collection
        if self._should_apply_tenant_filter():
            tenant_id = getattr(self, 'required_context').tenant_id
            collection = collection.with_tenant(str(tenant_id))

        # Update document in Weaviate
        await collection.data.update(
            properties=update_data,
            uuid=doc_id
        )
            
        return await self.get_by_id(doc_id)

    async def delete(self, doc_id: UUID) -> bool:
        self._check_initialized()
        await self._ensure_connected()
        
        try:
            # Check if document exists and is accessible
            existing_doc = await self.get_by_id(doc_id)
            if not existing_doc:
                return False
            
            # Get collection with tenant context if needed
            collection = self.collection
            if self._should_apply_tenant_filter():
                tenant_id = getattr(self, 'required_context').tenant_id
                collection = collection.with_tenant(str(tenant_id))
                
            # Delete document using the doc_id directly
            await collection.data.delete(doc_id)
            return True
            
        except Exception as e:
            logger.error(f"Error deleting document: {str(e)}")
            return False

    async def find_by_id_system(self, id: UUID) -> Optional[ModelType]:
        pass

    async def find_all_system(self, skip: int = 0, limit: int = 100, 
                            filters: Dict[str, Any] = None) -> List[ModelType]:
        pass

# Helper function to get all collections (fixed version)
async def get_all_collections() -> List[Dict[str, str]]:
    """Get all collections from Weaviate"""
    await ensure_client_connected()
    
    try:
        collections = await WeaviateDB.client.collections.list_all(simple=False)
        
        collections_list = []
        if isinstance(collections, dict):
            for collection_name, collection_obj in collections.items():
                collection_info = {
                    "name": collection_name,
                    "description": getattr(collection_obj, 'description', '') or f"Collection for {collection_name} data",
                    "properties":collection_obj.properties
                }
                collections_list.append(collection_info)
        
        return collections_list
        
    except Exception as e:
        logger.error(f"Error retrieving collections: {e}")
        return []

async def upsert_schemas():
    """
    Synchronize Weaviate schemas with model registry.
    Creates new collections, updates existing ones, and removes deprecated ones.
    """
    try:
        # Ensure client is connected
        if not WeaviateDB.client.is_connected():
            await WeaviateDB.client.connect()
            
        # Get existing collections
        existing_collections = await WeaviateDB.client.collections.list_all()
        
        # Extract existing collection names
        existing_class_names = set()
        if isinstance(existing_collections, dict):
            existing_class_names = set(existing_collections.keys())
        else:
            existing_class_names = {collection.get('class', collection.get('name', '')) 
                                  for collection in existing_collections.get('classes', [])} if hasattr(existing_collections, 'get') else set()

        # Get registered model collections
        registered_collections = set(model_registry.values())
        
        # Find collections to create, update, and delete
        collections_to_create = registered_collections - existing_class_names
        collections_to_update = registered_collections & existing_class_names
        collections_to_delete = existing_class_names - registered_collections
        
        # Create new collections
        for model_type, collection_name in model_registry.items():
            if collection_name in collections_to_create:
                try:
                    # Get properties from model schema
                    properties = _get_model_properties(model_type)
                    
                    # Check if model inherits from BaseModelWithTenant
                    is_tenant_model = issubclass(model_type, BaseModelWithTenant)
                    
                    # Create schema with multi-tenancy only for tenant-based models
                    config_args = {
                        "name": collection_name,
                        "properties": properties
                    }
                    
                    if is_tenant_model:
                        config_args["multi_tenancy_config"] = Configure.multi_tenancy(
                            enabled=True,
                            auto_tenant_creation=True
                        )
                    
                    await WeaviateDB.client.collections.create(**config_args)
                    logger.info(f"Created collection: {collection_name} {'with' if is_tenant_model else 'without'} multi-tenancy")
                except Exception as e:
                    logger.error(f"Error creating collection {collection_name}: {str(e)}")
                    continue
        
        # Update existing collections
        for model_type, collection_name in model_registry.items():
            if collection_name in collections_to_update:
                try:
                    # Get existing collection
                    collection = WeaviateDB.client.collections.get(collection_name)
            
                    # Get current collection config to check existing properties
                    config = await collection.config.get()
                    existing_properties = {prop.name for prop in config.properties}
            
                    # Get new properties from model
                    new_properties = _get_model_properties(model_type)
            
                    # Add only new properties that don't exist yet
                    for property in new_properties:
                        if property.name not in existing_properties:
                            await collection.config.add_property(property)
                            logger.info(f"Added new property {property.name} to collection {collection_name}")
            
                    # Check if model inherits from BaseModelWithTenant
                    is_tenant_model = issubclass(model_type, BaseModelWithTenant)
            
                    # Update multi-tenancy config if needed
                    if is_tenant_model:
                        multi_tenancy = Configure.multi_tenancy(
                            enabled=True,
                            auto_tenant_creation=True
                        )
                        await collection.config.update({"multi_tenancy": multi_tenancy})
                        logger.info(f"Updated multi-tenancy configuration for collection {collection_name}")
            
                except Exception as e:
                    logger.error(f"Error updating collection {collection_name}: {str(e)}")
                    continue
        
        # Delete deprecated collections
        for collection_name in collections_to_delete:
            try:
                await WeaviateDB.client.collections.delete(collection_name)
                logger.info(f"Deleted deprecated collection: {collection_name}")
            except Exception as e:
                logger.error(f"Error deleting collection {collection_name}: {str(e)}")
                continue
                
    except Exception as e:
        logger.error(f"Error in upsert_schemas: {str(e)}")
        raise

async def reset_schemas():
    """
    Reset all Weaviate schemas by deleting all collections and recreating them.
    """
    try:
        # Ensure client is connected
        if not WeaviateDB.client.is_connected():
            await WeaviateDB.client.connect()
            
        logger.info("Starting schema reset...")
        
        # Get existing collections
        existing_collections = await WeaviateDB.client.collections.list_all()
        
        # Delete all existing collections
        if isinstance(existing_collections, dict):
            collection_names = list(existing_collections.keys())
        else:
            collection_names = [
                collection.get('class', collection.get('name', '')) 
                for collection in existing_collections.get('classes', [])
            ] if hasattr(existing_collections, 'get') else []
            
        # Delete collections
        for collection_name in collection_names:
            try:
                await WeaviateDB.client.collections.delete(collection_name)
                logger.info(f"Deleted collection: {collection_name}")
            except Exception as e:
                logger.error(f"Error deleting collection {collection_name}: {str(e)}")
                continue
        
        logger.info("All collections deleted. Creating new schemas...")
        
        # Create new collections from model registry
        for model_type, collection_name in model_registry.items():
            try:
                # Get properties from model schema
                properties = _get_model_properties(model_type)
                
                # Check if model inherits from BaseModelWithTenant
                is_tenant_model = issubclass(model_type, BaseModelWithTenant)
                
                # Create schema with multi-tenancy only for tenant-based models
                config_args = {
                    "name": collection_name,
                    "properties": properties
                }
                
                if is_tenant_model:
                    config_args["multi_tenancy_config"] = Configure.multi_tenancy(
                        enabled=True,
                        auto_tenant_creation=True
                    )
                
                await WeaviateDB.client.collections.create(**config_args)
                logger.info(f"Created collection: {collection_name} {'with' if is_tenant_model else 'without'} multi-tenancy")
            except Exception as e:
                logger.error(f"Error creating collection {collection_name}: {str(e)}")
                continue
                
        logger.info("Schema reset completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in reset_schemas: {str(e)}")
        raise

async def reset_collection_schema(collection_name: str):
    """
    Reset a specific Weaviate collection schema by deleting and recreating it.
    
    Args:
        collection_name: Name of the collection to reset
        
    Raises:
        ValueError: If collection name is not found in the model registry
    """
    try:
        # Ensure client is connected
        if not WeaviateDB.client.is_connected():
            await WeaviateDB.client.connect()
            
        logger.info(f"Starting schema reset for collection: {collection_name}...")
        
        # Find the model type for this collection
        model_type = None
        for mt, cn in model_registry.items():
            if cn == collection_name:
                model_type = mt
                break
                
        if not model_type:
            raise ValueError(f"Collection {collection_name} not found in model registry")
        
        # Check if collection exists
        try:
            # Delete the collection if it exists
            await WeaviateDB.client.collections.delete(collection_name)
            logger.info(f"Deleted collection: {collection_name}")
        except Exception as e:
            logger.warning(f"Error deleting collection {collection_name}, it may not exist: {str(e)}")
        
        # Get properties from model schema
        properties = _get_model_properties(model_type)
        
        # Check if model inherits from BaseModelWithTenant
        is_tenant_model = issubclass(model_type, BaseModelWithTenant)
        
        # Create schema with multi-tenancy only for tenant-based models
        config_args = {
            "name": collection_name,
            "properties": properties
        }
        
        if is_tenant_model:
            config_args["multi_tenancy_config"] = Configure.multi_tenancy(
                enabled=True,
                auto_tenant_creation=True
            )
        
        await WeaviateDB.client.collections.create(**config_args)
        logger.info(f"Created collection: {collection_name} {'with' if is_tenant_model else 'without'} multi-tenancy")
            
        logger.info(f"Schema reset completed successfully for collection: {collection_name}!")
        return True
        
    except ValueError as ve:
        logger.error(f"Value Error in reset_collection_schema: {str(ve)}")
        raise
    except Exception as e:
        logger.error(f"Error in reset_collection_schema: {str(e)}")
        raise

def model_to_weaviate_properties(model: PydanticBaseModel) -> dict:
    """Convert Python model to Weaviate properties (exclude computed fields and id)"""
    # Identify computed fields using Pydantic v2's model_computed_fields
    computed_fields = set()
    if hasattr(model.__class__, 'model_computed_fields'):
        computed_fields = set(model.__class__.model_computed_fields.keys())

    # Exclude id field (handled separately by Weaviate) and computed fields
    exclude_fields = {"id"} | computed_fields

    return model.model_dump(exclude=exclude_fields)

def weaviate_obj_to_model(weaviate_obj: dict, model_class: type[PydanticBaseModel]) -> PydanticBaseModel:
    """Convert Weaviate object to Python model (simple approach)"""
    if not isinstance(weaviate_obj, dict):
        raise ValueError(f"Expected dict for weaviate_obj, got {type(weaviate_obj).__name__}")
    
    obj_id = weaviate_obj.get("id") or str(weaviate_obj.get("uuid", ""))
    properties = weaviate_obj.get("properties", {})
    
    if not isinstance(properties, dict):
        raise ValueError(f"Expected dict for properties, got {type(properties).__name__}")
    
    # Create model instance with id and properties
    try:
        return model_class(id=obj_id, **properties)
    except Exception as e:
        # Provide more context about the error
        raise ValueError(f"Failed to create {model_class.__name__} instance: {str(e)}")

def safe_weaviate_obj_to_model(weaviate_obj: dict, model_class: type[PydanticBaseModel], 
                              collection_name: str = None, obj_uuid: str = None) -> Optional[PydanticBaseModel]:
    """
    Safely convert Weaviate object to Python model with error handling.
    
    Args:
        weaviate_obj: The Weaviate object to convert
        model_class: The target model class
        collection_name: Optional collection name for logging
        obj_uuid: Optional object UUID for logging
        
    Returns:
        The converted model instance or None if conversion failed
    """
    try:
        result = weaviate_obj_to_model(weaviate_obj, model_class)
        return result
    except ValueError as e:
        # Handle validation errors (e.g., invalid field types, missing required fields)
        context_info = []
        if collection_name:
            context_info.append(f"collection={collection_name}")
        if obj_uuid:
            context_info.append(f"uuid={obj_uuid}")
        if weaviate_obj.get("id"):
            context_info.append(f"id={weaviate_obj.get('id')}")
        
        context_str = ", ".join(context_info) if context_info else "unknown context"
        logger.error(f"Validation error converting Weaviate object to model ({context_str}): {str(e)}")
        logger.debug(f"Failed object data: {weaviate_obj}")
        return None
    except TypeError as e:
        # Handle type conversion errors
        context_info = []
        if collection_name:
            context_info.append(f"collection={collection_name}")
        if obj_uuid:
            context_info.append(f"uuid={obj_uuid}")
        if weaviate_obj.get("id"):
            context_info.append(f"id={weaviate_obj.get('id')}")
        
        context_str = ", ".join(context_info) if context_info else "unknown context"
        logger.error(f"Type error converting Weaviate object to model ({context_str}): {str(e)}")
        logger.debug(f"Failed object data: {weaviate_obj}")
        return None
    except Exception as e:
        # Handle any other unexpected errors
        context_info = []
        if collection_name:
            context_info.append(f"collection={collection_name}")
        if obj_uuid:
            context_info.append(f"uuid={obj_uuid}")
        if weaviate_obj.get("id"):
            context_info.append(f"id={weaviate_obj.get('id')}")
        
        context_str = ", ".join(context_info) if context_info else "unknown context"
        logger.error(f"Unexpected error converting Weaviate object to model ({context_str}): {str(e)} (type: {type(e).__name__})")
        logger.debug(f"Failed object data: {weaviate_obj}")
        return None